import { CloseOutlined } from '@ant-design/icons';
import { Popconfirm } from 'antd';
import { useState } from 'react';

import { ConfirmDialogTexts } from '../constants';

export function CloseModalIcon({ changed, onClose }: { changed: boolean; onClose: () => void }) {
  const [open, setOpen] = useState(false);

  const confirm = () => {
    setOpen(false);
    onClose();
  };

  const cancel = () => {
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      setOpen(newOpen);
      return;
    }
    // 根据 changed 状态决定是否显示确认提示
    if (changed) {
      setOpen(newOpen); // 显示确认提示
    } else {
      onClose(); // 直接关闭
    }
  };

  return (
    <Popconfirm
      cancelText={ConfirmDialogTexts.cancelText}
      description={ConfirmDialogTexts.discardChanges.description}
      okText={ConfirmDialogTexts.discardChanges.confirmText}
      open={open}
      title={ConfirmDialogTexts.discardChanges.title}
      onCancel={cancel}
      onConfirm={confirm}
      onOpenChange={handleOpenChange}
    >
      <div style={{ cursor: 'pointer' }}>
        <CloseOutlined />
      </div>
    </Popconfirm>
  );
}
