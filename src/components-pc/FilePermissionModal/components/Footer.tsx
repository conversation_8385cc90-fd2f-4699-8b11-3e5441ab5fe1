import { Checkbox } from 'antd';

import { ConfirmDialogTexts } from '../constants';
import { ConfirmButton } from './ConfirmButton';
import styles from './Footer.less';
import { TooltipIcon } from './TooltipIcon';

export function Footer({
  showCheckbox,
  toolTips,
  onClose,
  changed,
  onSave,
}: {
  showCheckbox?: boolean;
  toolTips?: string;
  onClose: () => void;
  onSave?: () => void;
  changed: boolean;
}) {
  return (
    <footer className={styles.footer}>
      <div className={styles.footerLeft}>
        {showCheckbox && (
          <>
            <Checkbox>
              <span className={styles.inheritText}>继承上级目录权限设置</span>
            </Checkbox>
            <TooltipIcon height={20} toolTips={toolTips} width={20} />
          </>
        )}
      </div>
      <div className={styles.footerRight}>
        <ConfirmButton
          cancelText={ConfirmDialogTexts.cancelText}
          className={styles.saveBtn}
          confirmDescription={ConfirmDialogTexts.saveChanges.description}
          confirmText={ConfirmDialogTexts.saveChanges.confirmText}
          confirmTitle={ConfirmDialogTexts.saveChanges.title}
          needConfirm={changed}
          size="small"
          type="primary"
          onConfirm={() => {
            if (onSave) {
              onSave();
            }
            onClose();
          }}
        >
          保存修改
        </ConfirmButton>

        <ConfirmButton
          cancelText={ConfirmDialogTexts.cancelText}
          className={styles.cancelBtn}
          confirmDescription={ConfirmDialogTexts.discardChanges.description}
          confirmText={ConfirmDialogTexts.discardChanges.confirmText}
          confirmTitle={ConfirmDialogTexts.discardChanges.title}
          needConfirm={changed}
          size="small"
          onCancel={() => {
            // 用户选择"再想想"，不关闭弹框
          }}
          onConfirm={() => {
            // 当 changed 为 false 时，直接关闭弹框
            // 当 changed 为 true 时，用户确认放弃更改，也直接关闭弹框
            onClose();
          }}
        >
          取消
        </ConfirmButton>
      </div>
    </footer>
  );
}
